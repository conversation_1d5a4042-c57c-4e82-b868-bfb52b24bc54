// Mock data fixtures for testing

export const mockUsers = [
  {
    id: "user-1",
    email: "<EMAIL>",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    password: "hashedpassword123",
    role: "USER" as const,
    avatar: "https://example.com/avatar1.jpg",
    phone: "**********",
    dateOfBirth: new Date("1990-05-15"),
    gender: "MALE" as const,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "user-2",
    email: "<EMAIL>",
    name: "Admin User",
    password: "hashedpassword456",
    role: "ADMIN" as const,
    avatar: null,
    phone: "0987654321",
    dateOfBirth: new Date("1985-03-20"),
    gender: "FEMALE" as const,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "user-3",
    email: "<EMAIL>",
    name: "Test User",
    password: "hashedpassword789",
    role: "USER" as const,
    avatar: null,
    phone: "0555666777",
    dateOfBirth: null,
    gender: null,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

export const mockCategories = [
  {
    id: "cat-1",
    name: "Áo thun",
    description: "Áo thun thời trang cho nam và nữ",
    slug: "ao-thun",
    image: "https://example.com/category-tshirt.jpg",
    parentId: null,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "cat-2",
    name: "Quần jeans",
    description: "Quần jeans chất lượng cao",
    slug: "quan-jeans",
    image: "https://example.com/category-jeans.jpg",
    parentId: null,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "cat-3",
    name: "Áo thun nam",
    description: "Áo thun dành cho nam giới",
    slug: "ao-thun-nam",
    image: "https://example.com/category-men-tshirt.jpg",
    parentId: "cat-1",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

export const mockProducts = [
  {
    id: "prod-1",
    name: "Áo thun basic trắng",
    description: "Áo thun cotton 100% màu trắng, form regular fit",
    price: 299000,
    salePrice: 199000,
    images: [
      "https://example.com/product1-1.jpg",
      "https://example.com/product1-2.jpg",
    ],
    categoryId: "cat-3",
    stock: 50,
    sku: "AT-BASIC-WHITE-001",
    slug: "ao-thun-basic-trang",
    featured: true,
    status: "ACTIVE" as const,
    tags: ["basic", "cotton", "unisex"],
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "prod-2",
    name: "Quần jeans slim fit",
    description: "Quần jeans nam form slim fit, màu xanh đậm",
    price: 599000,
    salePrice: null,
    images: [
      "https://example.com/product2-1.jpg",
      "https://example.com/product2-2.jpg",
    ],
    categoryId: "cat-2",
    stock: 30,
    sku: "QJ-SLIM-BLUE-001",
    slug: "quan-jeans-slim-fit",
    featured: false,
    status: "ACTIVE" as const,
    tags: ["jeans", "slim", "men"],
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "prod-3",
    name: "Áo thun vintage",
    description: "Áo thun phong cách vintage với họa tiết độc đáo",
    price: 399000,
    salePrice: 299000,
    images: ["https://example.com/product3-1.jpg"],
    categoryId: "cat-1",
    stock: 0,
    sku: "AT-VINTAGE-001",
    slug: "ao-thun-vintage",
    featured: true,
    status: "OUT_OF_STOCK" as const,
    tags: ["vintage", "graphic", "limited"],
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "prod-4",
    name: "Áo sơ mi trắng",
    description: "Áo sơ mi trắng công sở, chất liệu cotton cao cấp",
    price: 450000,
    salePrice: null,
    images: ["https://example.com/product4-1.jpg"],
    categoryId: "cat-1",
    stock: 25,
    sku: "ASM-WHITE-001",
    slug: "ao-so-mi-trang",
    featured: false,
    status: "ACTIVE" as const,
    tags: ["formal", "cotton", "office"],
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "prod-5",
    name: "Quần short thể thao",
    description: "Quần short thể thao thoáng mát, phù hợp tập luyện",
    price: 199000,
    salePrice: 149000,
    images: ["https://example.com/product5-1.jpg"],
    categoryId: "cat-2",
    stock: 40,
    sku: "QS-SPORT-001",
    slug: "quan-short-the-thao",
    featured: true,
    status: "ACTIVE" as const,
    tags: ["sport", "shorts", "summer"],
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

export const mockOrders = [
  {
    id: "order-1",
    userId: "user-1",
    total: 498000,
    status: "PENDING" as const,
    paymentMethod: "COD" as const,
    paymentStatus: "PENDING" as const,
    shippingAddress: {
      fullName: "Nguyễn Văn A",
      phone: "**********",
      address: "123 Đường ABC",
      ward: "Phường XYZ",
      district: "Quận 1",
      province: "TP. Hồ Chí Minh",
    },
    billingAddress: undefined,
    notes: "Giao hàng giờ hành chính",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "order-2",
    userId: "user-1",
    total: 599000,
    status: "DELIVERED" as const,
    paymentMethod: "BANK_TRANSFER" as const,
    paymentStatus: "PAID" as const,
    shippingAddress: {
      fullName: "Nguyễn Văn A",
      phone: "**********",
      address: "456 Đường DEF",
      ward: "Phường ABC",
      district: "Quận 2",
      province: "TP. Hồ Chí Minh",
    },
    billingAddress: null,
    notes: null,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-12"),
  },
];

export const mockOrderItems = [
  {
    id: "item-1",
    orderId: "order-1",
    productId: "prod-1",
    quantity: 2,
    price: 199000,
    total: 398000,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "item-2",
    orderId: "order-1",
    productId: "prod-3",
    quantity: 1,
    price: 299000,
    total: 299000,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "item-3",
    orderId: "order-2",
    productId: "prod-2",
    quantity: 1,
    price: 599000,
    total: 599000,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-10"),
  },
];

export const mockCarts = [
  {
    id: "cart-1",
    userId: "user-1",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-16"),
  },
];

export const mockCartItems = [
  {
    id: "cart-item-1",
    cartId: "cart-1",
    productId: "prod-1",
    quantity: 1,
    price: 199000,
    createdAt: new Date("2024-01-16"),
    updatedAt: new Date("2024-01-16"),
  },
  {
    id: "cart-item-2",
    cartId: "cart-1",
    productId: "prod-2",
    quantity: 2,
    price: 599000,
    createdAt: new Date("2024-01-16"),
    updatedAt: new Date("2024-01-16"),
  },
];

export const mockAddresses = [
  {
    id: "addr-1",
    userId: "user-1",
    fullName: "Nguyễn Văn A",
    phone: "**********",
    address: "123 Đường ABC",
    ward: "Phường XYZ",
    district: "Quận 1",
    province: "TP. Hồ Chí Minh",
    isDefault: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "addr-2",
    userId: "user-1",
    fullName: "Nguyễn Văn A",
    phone: "**********",
    address: "456 Đường DEF",
    ward: "Phường ABC",
    district: "Quận 2",
    province: "TP. Hồ Chí Minh",
    isDefault: false,
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-05"),
  },
];

export const mockReviews = [
  {
    id: "review-1",
    userId: "user-1",
    productId: "prod-1",
    rating: 5,
    comment: "Sản phẩm rất tốt, chất lượng vải mềm mại",
    images: ["https://example.com/review1.jpg"],
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-12"),
  },
  {
    id: "review-2",
    userId: "user-1",
    productId: "prod-2",
    rating: 4,
    comment: "Quần đẹp, form chuẩn nhưng hơi dài",
    images: [],
    createdAt: new Date("2024-01-13"),
    updatedAt: new Date("2024-01-13"),
  },
];

// Search test data
export const mockSearchQueries = {
  valid: [
    { query: "áo", expectedCount: 3 }, // Should find "Áo thun basic", "Áo thun vintage", "Áo sơ mi"
    { query: "thun", expectedCount: 2 }, // Should find "Áo thun basic", "Áo thun vintage"
    { query: "quần", expectedCount: 2 }, // Should find "Quần jeans", "Quần short"
    { query: "cotton", expectedCount: 2 }, // Should find products with cotton tag
    { query: "basic", expectedCount: 1 }, // Should find "Áo thun basic"
  ],
  invalid: [
    { query: "", expectedCount: 0 },
    { query: "   ", expectedCount: 0 },
    { query: "nonexistent", expectedCount: 0 },
  ],
  filters: {
    category: "cat-1",
    minPrice: 200000,
    maxPrice: 400000,
    featured: true,
    inStock: true,
  },
};

// Profile test data
export const mockProfileUpdates = {
  valid: [
    {
      name: "Nguyễn Văn B",
      phone: "0987654321",
      dateOfBirth: "1992-08-20",
      gender: "MALE",
    },
    {
      name: "Trần Thị C",
      phone: "**********",
      dateOfBirth: "1995-12-10",
      gender: "FEMALE",
    },
    {
      name: "Lê Văn D",
      phone: "",
      dateOfBirth: "",
      gender: "OTHER",
    },
  ],
  invalid: [
    {
      name: "", // Empty name should fail
      phone: "0987654321",
      dateOfBirth: "1992-08-20",
      gender: "MALE",
    },
    {
      name: "Valid Name",
      phone: "invalid-phone",
      dateOfBirth: "invalid-date",
      gender: "INVALID_GENDER",
    },
  ],
};

// API Response mocks
export const mockApiResponses = {
  // Auth responses
  loginSuccess: {
    success: true,
    message: "Đăng nhập thành công",
    user: mockUsers[0],
  },
  loginError: {
    error: "Email hoặc mật khẩu không đúng",
  },

  // Product responses
  productsSuccess: {
    products: mockProducts,
    pagination: {
      total: mockProducts.length,
      page: 1,
      limit: 10,
      pages: 1,
    },
  },
  productSuccess: {
    product: mockProducts[0],
  },

  // Search responses
  searchSuccess: {
    products: mockProducts.slice(0, 2),
    pagination: {
      total: 2,
      page: 1,
      limit: 10,
      pages: 1,
    },
  },
  searchEmpty: {
    products: [],
    pagination: {
      total: 0,
      page: 1,
      limit: 10,
      pages: 0,
    },
  },

  // Profile responses
  profileSuccess: {
    ...mockUsers[0],
    addresses: mockAddresses.filter((addr) => addr.userId === "user-1"),
  },
  profileUpdateSuccess: {
    ...mockUsers[0],
    name: "Updated Name",
    phone: "0999888777",
  },
  profileError: {
    error: "Có lỗi xảy ra khi cập nhật thông tin cá nhân",
  },

  // Category responses
  categoriesSuccess: {
    categories: mockCategories,
  },

  // Order responses
  ordersSuccess: {
    orders: mockOrders,
    total: mockOrders.length,
  },
  orderSuccess: {
    order: mockOrders[0],
  },

  // Cart responses
  cartSuccess: {
    cart: {
      ...mockCarts[0],
      items: mockCartItems.map((item) => ({
        ...item,
        product: mockProducts.find((p) => p.id === item.productId),
      })),
    },
  },
};
