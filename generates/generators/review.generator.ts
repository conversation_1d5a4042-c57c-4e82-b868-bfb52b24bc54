import { faker } from "@faker-js/faker";
import { BaseGenerator, GeneratorConfig } from "./base/base.generator";
import reviewTemplates from "../templates/reviews/review-templates.json";

export interface ReviewData {
  userId: string;
  productId: string;
  rating: number;
  comment: string;
  images?: string[];
  createdAt: Date;
}

export interface ReviewGeneratorConfig extends GeneratorConfig {
  userIds?: string[];
  productIds?: string[];
  ratingDistribution?: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  verifiedPercentage?: number;
  includeHelpfulCount?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export class ReviewGenerator extends BaseGenerator<ReviewData> {
  private availableUsers: any[] = [];
  private availableProducts: any[] = [];
  private existingReviews: Set<string> = new Set();

  constructor(config: ReviewGeneratorConfig = {}) {
    super("Review", config);
    this.config = {
      ratingDistribution: {
        1: 5, // 5%
        2: 10, // 10%
        3: 15, // 15%
        4: 35, // 35%
        5: 35, // 35%
      },
      verifiedPercentage: 70, // 70% verified reviews
      includeHelpfulCount: true,
      dateRange: {
        start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
        end: new Date(),
      },
      ...config,
    };
  }

  private async loadUsersAndProducts(): Promise<void> {
    // Load users
    if (this.config.userIds && this.config.userIds.length > 0) {
      this.availableUsers = await this.prisma.user.findMany({
        where: {
          id: { in: this.config.userIds },
        },
      });
    } else {
      this.availableUsers = await this.prisma.user.findMany({
        where: {
          role: "USER",
        },
        take: 50, // Limit to avoid too many users
      });
    }

    // Load products
    if (this.config.productIds && this.config.productIds.length > 0) {
      this.availableProducts = await this.prisma.product.findMany({
        where: {
          id: { in: this.config.productIds },
        },
      });
    } else {
      this.availableProducts = await this.prisma.product.findMany({
        where: {
          status: "ACTIVE",
        },
        take: 100, // Limit to avoid too many products
      });
    }

    if (this.availableUsers.length === 0) {
      throw new Error("No users available for review generation");
    }

    if (this.availableProducts.length === 0) {
      throw new Error("No products available for review generation");
    }
  }

  private generateRating(): number {
    const distribution = this.config.ratingDistribution!;
    const random = Math.random() * 100;

    let cumulative = 0;
    for (const [rating, percentage] of Object.entries(distribution)) {
      cumulative += percentage;
      if (random <= cumulative) {
        return parseInt(rating);
      }
    }

    return 5; // Fallback
  }

  private generateComment(rating: number): string {
    const templates = reviewTemplates.reviewTemplates;

    let categoryTemplates: string[];

    if (rating >= 4) {
      categoryTemplates =
        rating === 5 ? templates.positive[5] : templates.positive[4];
    } else if (rating === 3) {
      categoryTemplates = templates.neutral[3];
    } else {
      categoryTemplates =
        rating === 2 ? templates.negative[2] : templates.negative[1];
    }

    let comment = faker.helpers.arrayElement(categoryTemplates);

    // Sometimes enhance comment with specific aspects
    if (Math.random() > 0.6) {
      const aspects = reviewTemplates.reviewAspects;
      const aspectTypes = Object.keys(aspects);
      const randomAspectType = faker.helpers.arrayElement(aspectTypes);
      const randomAspect = faker.helpers.arrayElement(
        aspects[randomAspectType as keyof typeof aspects]
      );

      // Add aspect to comment
      const enhancers = [
        ` ${randomAspect}.`,
        ` Đặc biệt ${randomAspect}.`,
        ` Mình thích nhất là ${randomAspect}.`,
      ];

      comment += faker.helpers.arrayElement(enhancers);
    }

    return comment;
  }

  private generateReviewDate(): Date {
    const { start, end } = this.config.dateRange!;
    return faker.date.between({ from: start, to: end });
  }

  private generateUniqueReviewKey(userId: string, productId: string): string {
    return `${userId}-${productId}`;
  }

  async generateData(): Promise<ReviewData[]> {
    await this.loadUsersAndProducts();

    const reviews: ReviewData[] = [];
    const maxAttempts = (this.config.count || 10) * 3; // Allow more attempts to find unique combinations
    let attempts = 0;

    while (
      reviews.length < (this.config.count || 10) &&
      attempts < maxAttempts
    ) {
      attempts++;

      const user = faker.helpers.arrayElement(this.availableUsers);
      const product = faker.helpers.arrayElement(this.availableProducts);
      const reviewKey = this.generateUniqueReviewKey(user.id, product.id);

      // Skip if this user already reviewed this product
      if (this.existingReviews.has(reviewKey)) {
        continue;
      }

      this.existingReviews.add(reviewKey);

      const rating = this.generateRating();
      const comment = this.generateComment(rating);

      const reviewData: ReviewData = {
        userId: user.id,
        productId: product.id,
        rating,
        comment,
        images: [], // Add empty images array
        createdAt: this.generateReviewDate(),
      };

      if (this.config.includeHelpfulCount) {
        // Generate helpful count based on rating (higher rating = more helpful votes)
        const maxHelpful = rating >= 4 ? 20 : rating >= 3 ? 10 : 5;
        reviewData.helpfulCount = faker.number.int({ min: 0, max: maxHelpful });
        reviewData.isHelpful = reviewData.helpfulCount > 0;
      }

      reviews.push(reviewData);
    }

    if (reviews.length === 0) {
      throw new Error(
        "Could not generate any unique reviews. Try increasing user or product count."
      );
    }

    return reviews;
  }

  async createRecord(data: ReviewData): Promise<any> {
    // Check if review already exists
    const existingReview = await this.prisma.review.findFirst({
      where: {
        userId: data.userId,
        productId: data.productId,
      },
    });

    if (existingReview) {
      this.log(
        `Review from user ${data.userId} for product ${data.productId} already exists, skipping...`,
        "warning"
      );
      return existingReview;
    }

    const review = await this.prisma.review.create({
      data: {
        userId: data.userId,
        productId: data.productId,
        rating: data.rating,
        comment: data.comment,
        images: data.images || [],
        createdAt: data.createdAt,
      },
    });

    // Update product's average rating and review count
    await this.updateProductRating(data.productId);

    return review;
  }

  private async updateProductRating(productId: string): Promise<void> {
    const reviews = await this.prisma.review.findMany({
      where: { productId },
      select: { rating: true },
    });

    if (reviews.length > 0) {
      const avgRating =
        reviews.reduce((sum, review) => sum + review.rating, 0) /
        reviews.length;

      await this.prisma.product.update({
        where: { id: productId },
        data: {
          avgRating: Math.round(avgRating * 10) / 10, // Round to 1 decimal place
          reviewCount: reviews.length,
        },
      });
    }
  }

  validateData(data: ReviewData): boolean {
    if (!data.userId || !data.productId) {
      this.log("User ID and Product ID are required", "error");
      return false;
    }

    if (data.rating < 1 || data.rating > 5) {
      this.log("Rating must be between 1 and 5", "error");
      return false;
    }

    if (!data.comment || data.comment.trim().length === 0) {
      this.log("Comment is required", "error");
      return false;
    }

    if (data.comment.length > 1000) {
      this.log("Comment is too long (max 1000 characters)", "error");
      return false;
    }

    return true;
  }

  async checkExisting(where: any): Promise<boolean> {
    const existing = await this.prisma.review.findFirst({ where });
    return !!existing;
  }
}
