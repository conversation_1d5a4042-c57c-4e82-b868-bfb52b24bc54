import { faker } from "@faker-js/faker";
import { BaseGenerator, GeneratorConfig } from "./base/base.generator";
import { ValidationUtils } from "../utils/validation.utils";
import { ImageGenerator } from "../utils/image.utils";
import { generateSlug, generateUniqueSKU } from "../utils/slug.utils";
import productNamesData from "../data/product-names.json";

export interface ProductData {
  name: string;
  slug: string;
  description: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  categoryId: string;
  images: string[];
  featured: boolean;
  status: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  tags?: string[];
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  variants?: {
    size?: string[];
    color?: string[];
  };
}

export interface ProductGeneratorConfig extends GeneratorConfig {
  categoryIds?: string[];
  includeVariants?: boolean;
  includeDimensions?: boolean;
  salePercentage?: number; // Percentage of products on sale
  featuredPercentage?: number; // Percentage of featured products
  minPrice?: number;
  maxPrice?: number;
  minStock?: number;
  maxStock?: number;
}

export class ProductGenerator extends BaseGenerator<ProductData> {
  private existingSlugs: Set<string> = new Set();
  private existingSKUs: Set<string> = new Set();
  private availableCategories: any[] = [];

  constructor(config: ProductGeneratorConfig = {}) {
    super("Product", config);
    this.config = {
      includeVariants: true,
      includeDimensions: false,
      salePercentage: 30, // 30% of products on sale
      featuredPercentage: 20, // 20% featured
      minPrice: 50000,
      maxPrice: 2000000,
      minStock: 0,
      maxStock: 100,
      ...config,
    };
  }

  private get productConfig(): ProductGeneratorConfig {
    return this.config as ProductGeneratorConfig;
  }

  private async loadCategories(): Promise<void> {
    if (
      this.productConfig.categoryIds &&
      this.productConfig.categoryIds.length > 0
    ) {
      this.availableCategories = await this.prisma.category.findMany({
        where: {
          id: { in: this.productConfig.categoryIds },
        },
      });
    } else {
      this.availableCategories = await this.prisma.category.findMany({
        where: {
          parentId: { not: null }, // Only leaf categories
        },
      });
    }

    if (this.availableCategories.length === 0) {
      throw new Error("No categories available for product generation");
    }
  }

  private generateProductName(categorySlug: string): string {
    const categoryNames =
      productNamesData.productNames[
        categorySlug as keyof typeof productNamesData.productNames
      ];

    if (categoryNames && categoryNames.length > 0) {
      const baseName = faker.helpers.arrayElement(categoryNames);

      // Sometimes add adjective or material
      if (Math.random() > 0.7) {
        const adjective = faker.helpers.arrayElement(
          productNamesData.adjectives
        );
        return `${baseName} ${adjective}`;
      }

      if (Math.random() > 0.8) {
        const material = faker.helpers.arrayElement(productNamesData.materials);
        return `${baseName} ${material}`;
      }

      return baseName;
    }

    // Fallback to generic fashion product names
    const genericNames = [
      "Sản phẩm thời trang cao cấp",
      "Item thời trang trendy",
      "Món đồ phong cách",
      "Sản phẩm hot trend",
    ];

    return faker.helpers.arrayElement(genericNames);
  }

  private generateProductDescription(
    name: string,
    _categorySlug: string
  ): string {
    const materials = productNamesData.materials;
    const colors = productNamesData.colors;

    const material = faker.helpers.arrayElement(materials);
    const color = faker.helpers.arrayElement(colors);

    const descriptions = [
      `${name} được làm từ chất liệu ${material} cao cấp, màu ${color} thời trang. Thiết kế hiện đại, phù hợp với nhiều dịp khác nhau.`,
      `Sản phẩm ${name} với chất liệu ${material} mềm mại, thoải mái. Màu sắc ${color} dễ phối đồ, phong cách trẻ trung.`,
      `${name} chất liệu ${material} bền đẹp, form dáng chuẩn. Màu ${color} sang trọng, phù hợp cho cả nam và nữ.`,
      `Thiết kế ${name} từ ${material} chất lượng cao, màu ${color} nổi bật. Phong cách thời trang, dễ mix & match.`,
    ];

    return faker.helpers.arrayElement(descriptions);
  }

  private generatePrice(): { price: number; salePrice?: number } {
    const price = faker.number.int({
      min: this.productConfig.minPrice || 50000,
      max: this.productConfig.maxPrice || 2000000,
    });

    // Round to nearest 1000
    const roundedPrice = Math.round(price / 1000) * 1000;

    // Determine if on sale
    const isOnSale =
      Math.random() < (this.productConfig.salePercentage || 30) / 100;

    if (isOnSale) {
      const discountPercent = faker.number.int({ min: 10, max: 50 });
      const salePrice =
        Math.round((roundedPrice * (100 - discountPercent)) / 100 / 1000) *
        1000;
      return { price: roundedPrice, salePrice };
    }

    return { price: roundedPrice };
  }

  private generateTags(categorySlug: string, productName: string): string[] {
    const baseTags = ["thời trang", "hot trend", "chất lượng cao"];

    // Add category-specific tags
    const categoryTags: Record<string, string[]> = {
      "ao-nu": ["áo nữ", "thời trang nữ", "công sở"],
      "quan-nu": ["quần nữ", "bottom", "casual"],
      "vay-dam": ["váy đầm", "dự tiệc", "nữ tính"],
      "giay-nu": ["giày nữ", "footwear", "phụ kiện"],
      "ao-nam": ["áo nam", "thời trang nam", "menswear"],
      "quan-nam": ["quần nam", "bottom nam", "casual"],
      "giay-nam": ["giày nam", "giày thể thao", "nam tính"],
      "phu-kien": ["phụ kiện", "accessories", "trang sức"],
    };

    const specificTags = categoryTags[categorySlug] || ["thời trang"];

    // Add material and color tags
    if (productName.includes("Cotton")) baseTags.push("cotton");
    if (productName.includes("Jean")) baseTags.push("denim", "jean");
    if (productName.includes("Cao Cấp")) baseTags.push("premium", "luxury");

    return [...baseTags, ...specificTags].slice(0, 6);
  }

  private generateVariants(categorySlug: string): {
    size?: string[];
    color?: string[];
  } {
    const variants: { size?: string[]; color?: string[] } = {};

    // Size variants based on category
    const sizeVariants: Record<string, string[]> = {
      "ao-nu": ["S", "M", "L", "XL"],
      "quan-nu": ["S", "M", "L", "XL", "XXL"],
      "vay-dam": ["S", "M", "L", "XL"],
      "giay-nu": ["35", "36", "37", "38", "39", "40"],
      "ao-nam": ["M", "L", "XL", "XXL"],
      "quan-nam": ["M", "L", "XL", "XXL", "XXXL"],
      "giay-nam": ["39", "40", "41", "42", "43", "44"],
      "phu-kien": ["One Size"],
    };

    if (sizeVariants[categorySlug]) {
      variants.size = sizeVariants[categorySlug];
    }

    // Color variants
    const colors = ["Đen", "Trắng", "Xám", "Nâu", "Hồng", "Xanh Navy"];
    variants.color = faker.helpers.arrayElements(colors, { min: 2, max: 4 });

    return variants;
  }

  async generateData(): Promise<ProductData[]> {
    await this.loadCategories();

    const products: ProductData[] = [];

    for (let i = 0; i < (this.productConfig.count || 10); i++) {
      const category = faker.helpers.arrayElement(this.availableCategories);
      const name = this.generateProductName(category.slug);
      const slug = generateSlug(name);
      const uniqueSlug = this.generateUniqueSlug(slug);
      const pricing = this.generatePrice();

      const productData: ProductData = {
        name,
        slug: uniqueSlug,
        description: this.generateProductDescription(name, category.slug),
        price: pricing.price,
        salePrice: pricing.salePrice,
        sku: generateUniqueSKU(
          category.slug.toUpperCase(),
          Array.from(this.existingSKUs)
        ),
        stock: faker.number.int({
          min: this.productConfig.minStock || 0,
          max: this.productConfig.maxStock || 100,
        }),
        categoryId: category.id,
        images: ImageGenerator.generateFashionImages(
          category.slug,
          faker.number.int({ min: 1, max: 4 })
        ),
        featured:
          Math.random() < (this.productConfig.featuredPercentage || 20) / 100,
        status: faker.helpers.weightedArrayElement([
          { weight: 85, value: "ACTIVE" },
          { weight: 10, value: "OUT_OF_STOCK" },
          { weight: 5, value: "INACTIVE" },
        ]) as "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK",
        tags: this.generateTags(category.slug, name),
        weight: faker.number.float({ min: 0.1, max: 5.0, fractionDigits: 2 }),
      };

      if (this.productConfig.includeVariants) {
        productData.variants = this.generateVariants(category.slug);
      }

      if (this.productConfig.includeDimensions) {
        productData.dimensions = {
          length: faker.number.float({ min: 10, max: 100, fractionDigits: 1 }),
          width: faker.number.float({ min: 10, max: 80, fractionDigits: 1 }),
          height: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
        };
      }

      this.existingSKUs.add(productData.sku);
      products.push(productData);
    }

    return products;
  }

  private generateUniqueSlug(baseSlug: string): string {
    let slug = baseSlug;
    let counter = 1;

    while (this.existingSlugs.has(slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    this.existingSlugs.add(slug);
    return slug;
  }

  async createRecord(data: ProductData): Promise<any> {
    // Check if product already exists
    const existingProduct = await this.prisma.product.findUnique({
      where: { slug: data.slug },
    });

    if (existingProduct) {
      this.log(
        `Product with slug ${data.slug} already exists, skipping...`,
        "warning"
      );
      return existingProduct;
    }

    return await this.prisma.product.create({
      data: {
        name: data.name,
        slug: data.slug,
        description: data.description,
        price: data.price,
        salePrice: data.salePrice,
        sku: data.sku,
        stock: data.stock,
        categoryId: data.categoryId,
        images: data.images,
        featured: data.featured,
        status: data.status,
        tags: data.tags,
        weight: data.weight,
        dimensions: data.dimensions,
        variants: data.variants,
      },
    });
  }

  validateData(data: ProductData): boolean {
    const validation = ValidationUtils.validateProductData(data);

    if (!validation.isValid) {
      this.log(`Validation failed: ${validation.errors.join(", ")}`, "error");
      return false;
    }

    return true;
  }

  async checkExisting(where: any): Promise<boolean> {
    const existing = await this.prisma.product.findUnique({ where });
    return !!existing;
  }
}
