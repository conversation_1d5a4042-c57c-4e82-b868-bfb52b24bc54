import { faker } from "@faker-js/faker";
import { BaseGenerator, GeneratorConfig } from "./base/base.generator";
import { generateSlug } from "../utils/slug.utils";
import categoriesData from "../data/categories.json";

export interface CategoryData {
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  image?: string;
  isActive: boolean;
  sortOrder: number;
}

export interface CategoryGeneratorConfig extends GeneratorConfig {
  useDefaultCategories?: boolean;
  includeImages?: boolean;
  maxDepth?: number;
}

export class CategoryGenerator extends BaseGenerator<CategoryData> {
  private existingSlugs: Set<string> = new Set();
  private createdCategories: Map<string, any> = new Map();

  constructor(config: CategoryGeneratorConfig = {}) {
    super("Category", config);
    this.config = {
      useDefaultCategories: true,
      includeImages: false,
      maxDepth: 2,
      ...config,
    };
  }

  private generateCategoryImage(categoryName: string): string {
    // Generate category-specific images
    const categoryKeywords: Record<string, string[]> = {
      "thời trang nữ": ["women fashion", "female clothing"],
      "thời trang nam": ["men fashion", "male clothing"],
      "phụ kiện": ["accessories", "jewelry"],
      "thể thao": ["sports", "fitness"],
      "trẻ em": ["kids fashion", "children clothing"],
      áo: ["shirt", "top", "clothing"],
      quần: ["pants", "trousers"],
      giày: ["shoes", "footwear"],
      túi: ["bag", "handbag"],
      "trang sức": ["jewelry", "accessories"],
    };

    const keywords = Object.keys(categoryKeywords).find((key) =>
      categoryName.toLowerCase().includes(key)
    );

    if (keywords) {
      const searchTerms = categoryKeywords[keywords];
      const term = faker.helpers.arrayElement(searchTerms);
      return `https://images.unsplash.com/photo-${faker.string.numeric(13)}-${faker.string.alphanumeric(12)}?w=400&h=300&q=${encodeURIComponent(term)}`;
    }

    return `https://images.unsplash.com/photo-${faker.string.numeric(13)}-${faker.string.alphanumeric(12)}?w=400&h=300&q=fashion`;
  }

  private async createCategoryFromData(
    categoryData: any,
    parentId?: string,
    sortOrder: number = 0
  ): Promise<CategoryData> {
    const slug = categoryData.slug;

    if (this.existingSlugs.has(slug)) {
      throw new Error(`Duplicate slug: ${slug}`);
    }

    this.existingSlugs.add(slug);

    const category: CategoryData = {
      name: categoryData.name,
      slug: slug,
      description: categoryData.description,
      parentId: parentId,
      isActive: true,
      sortOrder: sortOrder,
    };

    if (this.config.includeImages) {
      category.image = this.generateCategoryImage(category.name);
    }

    return category;
  }

  async generateData(): Promise<CategoryData[]> {
    const categories: CategoryData[] = [];

    if (this.config.useDefaultCategories) {
      // Use predefined categories from JSON
      for (let i = 0; i < categoriesData.categories.length; i++) {
        const categoryData = categoriesData.categories[i];

        // Create parent category
        const parentCategory = await this.createCategoryFromData(
          categoryData,
          undefined,
          i
        );
        categories.push(parentCategory);

        // Create child categories
        if (categoryData.children && categoryData.children.length > 0) {
          for (let j = 0; j < categoryData.children.length; j++) {
            const childData = categoryData.children[j];
            const childCategory = await this.createCategoryFromData(
              childData,
              "PARENT_PLACEHOLDER",
              j
            );
            childCategory.parentId = parentCategory.slug; // Will be resolved later
            categories.push(childCategory);
          }
        }
      }
    } else {
      // Generate random categories
      const fashionCategories = [
        "Thời trang cao cấp",
        "Thời trang công sở",
        "Thời trang dạo phố",
        "Thời trang dự tiệc",
        "Thời trang mùa hè",
        "Thời trang mùa đông",
        "Thời trang vintage",
        "Thời trang hiện đại",
        "Thời trang tối giản",
      ];

      for (let i = 0; i < (this.config.count || 5); i++) {
        const name = faker.helpers.arrayElement(fashionCategories);
        const slug = generateSlug(name);

        if (this.existingSlugs.has(slug)) {
          continue;
        }

        this.existingSlugs.add(slug);

        const category: CategoryData = {
          name: name,
          slug: slug,
          description: faker.commerce.productDescription(),
          isActive: Math.random() > 0.1, // 90% active
          sortOrder: i,
        };

        if (this.config.includeImages) {
          category.image = this.generateCategoryImage(category.name);
        }

        categories.push(category);
      }
    }

    return categories;
  }

  async createRecord(data: CategoryData): Promise<any> {
    // Check if category already exists
    const existingCategory = await this.prisma.category.findUnique({
      where: { slug: data.slug },
    });

    if (existingCategory) {
      this.log(
        `Category with slug ${data.slug} already exists, skipping...`,
        "warning"
      );
      this.createdCategories.set(data.slug, existingCategory);
      return existingCategory;
    }

    // Handle parent relationship
    let parentId: string | undefined;
    if (data.parentId && data.parentId !== "PARENT_PLACEHOLDER") {
      const parentCategory = this.createdCategories.get(data.parentId);
      if (parentCategory) {
        parentId = parentCategory.id;
      } else {
        // Try to find parent in database
        const parent = await this.prisma.category.findUnique({
          where: { slug: data.parentId },
        });
        if (parent) {
          parentId = parent.id;
          this.createdCategories.set(data.parentId, parent);
        }
      }
    }

    const category = await this.prisma.category.create({
      data: {
        name: data.name,
        slug: data.slug,
        description: data.description,
        parentId: parentId,
        image: data.image,
        isActive: data.isActive,
        sortOrder: data.sortOrder,
      },
    });

    this.createdCategories.set(data.slug, category);
    return category;
  }

  validateData(data: CategoryData): boolean {
    if (!data.name || data.name.trim().length === 0) {
      this.log("Category name is required", "error");
      return false;
    }

    if (!data.slug || data.slug.trim().length === 0) {
      this.log("Category slug is required", "error");
      return false;
    }

    if (!/^[a-z0-9-]+$/.test(data.slug)) {
      this.log(
        "Category slug must contain only lowercase letters, numbers, and hyphens",
        "error"
      );
      return false;
    }

    return true;
  }

  async checkExisting(where: any): Promise<boolean> {
    const existing = await this.prisma.category.findUnique({ where });
    return !!existing;
  }

  // Override generate method to handle parent-child relationships properly
  async generate(): Promise<any> {
    this.log(`Starting generation of categories...`);

    try {
      const { result, duration } = await this.measureTime(async () => {
        const generatedData = await this.generateData();
        const createdRecords = [];

        // First pass: create parent categories
        const parentCategories = generatedData.filter(
          (cat) => !cat.parentId || cat.parentId === "PARENT_PLACEHOLDER"
        );
        for (const data of parentCategories) {
          if (!this.validateData(data)) {
            this.log(`Invalid data generated, skipping...`, "warning");
            continue;
          }

          try {
            const record = await this.createRecord(data);
            createdRecords.push(record);
            this.log(`Created parent category: ${record.name}`, "success");
          } catch (error) {
            this.log(`Failed to create parent category: ${error}`, "error");
          }
        }

        // Second pass: create child categories
        const childCategories = generatedData.filter(
          (cat) => cat.parentId && cat.parentId !== "PARENT_PLACEHOLDER"
        );
        for (const data of childCategories) {
          if (!this.validateData(data)) {
            this.log(`Invalid data generated, skipping...`, "warning");
            continue;
          }

          try {
            const record = await this.createRecord(data);
            createdRecords.push(record);
            this.log(`Created child category: ${record.name}`, "success");
          } catch (error) {
            this.log(`Failed to create child category: ${error}`, "error");
          }
        }

        return createdRecords;
      });

      this.log(
        `Generated ${result.length} categories in ${duration}ms`,
        "success"
      );

      return {
        success: true,
        data: result,
        count: result.length,
        duration,
      };
    } catch (error) {
      this.log(`Generation failed: ${error}`, "error");
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        count: 0,
        duration: 0,
      };
    }
  }
}
