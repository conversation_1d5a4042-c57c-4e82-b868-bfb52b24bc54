import { PrismaClient } from "@prisma/client";
import { faker } from "@faker-js/faker";
import chalk from "chalk";

export interface GeneratorConfig {
  count?: number;
  locale?: string;
  seed?: number;
  verbose?: boolean;
}

export interface GeneratorResult<T = any> {
  success: boolean;
  data?: T[];
  error?: string;
  count: number;
  duration: number;
}

export abstract class BaseGenerator<T = any> {
  protected prisma: PrismaClient;
  protected config: GeneratorConfig;
  protected name: string;

  constructor(name: string, config: GeneratorConfig = {}) {
    this.name = name;
    this.config = {
      count: 10,
      locale: "vi",
      seed: Date.now(),
      verbose: false,
      ...config,
    };

    this.prisma = new PrismaClient();
    this.setupFaker();
  }

  private setupFaker(): void {
    // Set locale for Vietnamese data
    faker.setLocale(this.config.locale || "vi");

    // Set seed for reproducible results
    if (this.config.seed) {
      faker.seed(this.config.seed);
    }
  }

  protected log(
    message: string,
    type: "info" | "success" | "error" | "warning" = "info"
  ): void {
    if (!this.config.verbose) return;

    const colors = {
      info: chalk.blue,
      success: chalk.green,
      error: chalk.red,
      warning: chalk.yellow,
    };

    const prefix = `[${this.name}]`;
    console.log(colors[type](`${prefix} ${message}`));
  }

  protected async measureTime<R>(
    operation: () => Promise<R>
  ): Promise<{ result: R; duration: number }> {
    const startTime = Date.now();
    const result = await operation();
    const duration = Date.now() - startTime;
    return { result, duration };
  }

  protected generateSlug(text: string): string {
    return text
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  protected generateSKU(prefix: string, length: number = 6): string {
    const randomPart = Math.random()
      .toString(36)
      .substr(2, length)
      .toUpperCase();
    return `${prefix}-${randomPart}`;
  }

  protected getRandomElements<T>(array: T[], count: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  protected async checkExisting(_where: any): Promise<boolean> {
    // This should be implemented by child classes
    return false;
  }

  // Abstract methods that must be implemented by child classes
  abstract generateData(): Promise<T[]>;
  abstract createRecord(data: T): Promise<any>;
  abstract validateData(data: T): boolean;

  async generate(): Promise<GeneratorResult<T>> {
    this.log(
      `Starting generation of ${this.config.count} ${this.name} records...`
    );

    try {
      const { result, duration } = await this.measureTime(async () => {
        const generatedData = await this.generateData();
        const createdRecords = [];

        for (const data of generatedData) {
          if (!this.validateData(data)) {
            this.log(`Invalid data generated, skipping...`, "warning");
            continue;
          }

          try {
            const record = await this.createRecord(data);
            createdRecords.push(record);
            this.log(
              `Created ${this.name}: ${record.name || record.id}`,
              "success"
            );
          } catch (error) {
            this.log(`Failed to create ${this.name}: ${error}`, "error");
          }
        }

        return createdRecords;
      });

      this.log(
        `Generated ${result.length} ${this.name} records in ${duration}ms`,
        "success"
      );

      return {
        success: true,
        data: result,
        count: result.length,
        duration,
      };
    } catch (error) {
      this.log(`Generation failed: ${error}`, "error");
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        count: 0,
        duration: 0,
      };
    }
  }

  async cleanup(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
