#!/usr/bin/env tsx

import { DataGenerator } from './index';

async function main() {
  console.log('🚀 Starting NS Shop data generation...');
  
  const generator = new DataGenerator({
    users: {
      count: 15,
      includeAdmin: true,
      includeAddress: true,
      includeAvatar: true,
    },
    products: {
      count: 30,
      salePercentage: 25,
      featuredPercentage: 15,
    },
    reviews: {
      count: 60,
    },
    orders: {
      count: 20,
    },
    global: {
      verbose: true,
    },
  });

  try {
    const result = await generator.generateAll();
    
    if (result.success) {
      console.log('✅ Data generation completed successfully!');
    } else {
      console.log('⚠️ Data generation completed with errors:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }
  } catch (error) {
    console.error('❌ Data generation failed:', error);
    process.exit(1);
  } finally {
    await generator.cleanup();
  }
}

if (require.main === module) {
  main();
}
